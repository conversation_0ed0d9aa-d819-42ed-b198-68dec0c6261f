"""
评论数据模型
"""

from sqlalchemy import Column, String, Integer, DateTime, Text, JSON
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, ContentStatus


class Comment(BaseModel):
    """评论模型"""
    __tablename__ = "comments"
    
    platform = Column(String(50), nullable=False, comment="平台名称")
    content = Column(Text, nullable=False, comment="评论内容")
    likes = Column(Integer, default=0, comment="点赞数")
    timestamp = Column(DateTime, nullable=False, comment="评论时间戳")
    keywords = Column(JSON, comment="关键词列表")
    status = Column(String(20), default=ContentStatus.PENDING.value, comment="处理状态")
    
    # 关联关系
    enhanced_stories = relationship("EnhancedStory", back_populates="original_comment")
    
    def __repr__(self):
        return f"<Comment(id={self.id}, platform={self.platform}, status={self.status})>"