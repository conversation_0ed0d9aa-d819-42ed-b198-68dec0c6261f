"""
数据库连接和初始化模块
"""

import asyncio
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.core.config import settings
from app.core.logger import logger

# 数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
    echo=settings.DEBUG
)

# 会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 基础模型类
Base = declarative_base()

# 元数据
metadata = MetaData()


async def init_db():
    """初始化数据库"""
    try:
        logger.info("正在创建数据库表...")
        
        # 导入所有模型以确保表被创建
        from app.models import comment, story, scene, asset, content, publish
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        logger.info("数据库表创建完成")
        
        # 创建必要的目录
        import os
        directories = [
            settings.MEDIA_ROOT,
            settings.STATIC_ROOT,
            f"{settings.MEDIA_ROOT}/images",
            f"{settings.MEDIA_ROOT}/videos",
            f"{settings.MEDIA_ROOT}/audio",
            f"{settings.MEDIA_ROOT}/temp"
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"创建目录: {directory}")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal
    
    def get_session(self):
        """获取数据库会话"""
        return SessionLocal()
    
    def close_session(self, session):
        """关闭数据库会话"""
        session.close()
    
    async def execute_query(self, query: str, params: dict = None):
        """执行查询"""
        with self.get_session() as session:
            try:
                result = session.execute(query, params or {})
                session.commit()
                return result
            except Exception as e:
                session.rollback()
                logger.error(f"查询执行失败: {e}")
                raise
    
    async def health_check(self) -> bool:
        """数据库健康检查"""
        try:
            with self.get_session() as session:
                session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")
            return False


# 全局数据库管理器实例
db_manager = DatabaseManager()