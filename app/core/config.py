"""
配置管理模块
"""

import os
from typing import List, Optional
from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = "StoryForge"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./storyforge.db"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # API密钥配置
    DEEPSEEK_API_KEY: Optional[str] = None
    DEEPSEEK_BASE_URL: str = "https://api.deepseek.com"
    
    DOUBAO_API_KEY: Optional[str] = None
    DOUBAO_BASE_URL: str = "https://ark.cn-beijing.volces.com"
    
    VIDU_API_KEY: Optional[str] = None
    VIDU_BASE_URL: str = "https://api.vidu.ai"
    
    JIANYING_API_KEY: Optional[str] = None
    
    # 爬虫配置
    CRAWLER_KEYWORDS: List[str] = ["灵异", "前任", "社死", "奇葩", "搞笑"]
    CRAWLER_PLATFORMS: List[str] = ["douyin", "bilibili", "xiaohongshu"]
    CRAWLER_INTERVAL: int = 3600  # 爬取间隔（秒）
    
    # 内容生成配置
    STORY_STYLES: List[str] = ["悬疑惊悚", "都市言情", "搞笑日常", "温馨治愈"]
    DEFAULT_STYLE: str = "悬疑惊悚"
    
    # 视频配置
    VIDEO_RESOLUTION: str = "1080x1920"  # 竖屏格式
    VIDEO_FPS: int = 30
    VIDEO_BITRATE: str = "2M"
    
    # 文件存储配置
    MEDIA_ROOT: str = "./media"
    STATIC_ROOT: str = "./static"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/storyforge.log"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


class CrawlerConfig:
    """爬虫配置"""
    def __init__(self):
        self.keywords = settings.CRAWLER_KEYWORDS
        self.platforms = settings.CRAWLER_PLATFORMS
        self.interval = settings.CRAWLER_INTERVAL
        self.max_comments_per_run = 100
        self.min_likes = 10


class DeepSeekConfig:
    """DeepSeek LLM配置"""
    def __init__(self):
        self.api_key = settings.DEEPSEEK_API_KEY
        self.base_url = settings.DEEPSEEK_BASE_URL
        self.model = "deepseek-chat"
        self.max_tokens = 2000
        self.temperature = 0.7


class DoubaoConfig:
    """豆包API配置"""
    def __init__(self):
        self.api_key = settings.DOUBAO_API_KEY
        self.base_url = settings.DOUBAO_BASE_URL
        self.model = "doubao-pro-32k"
        self.image_size = "1024x1024"


class ViduConfig:
    """Vidu API配置"""
    def __init__(self):
        self.api_key = settings.VIDU_API_KEY
        self.base_url = settings.VIDU_BASE_URL
        self.model = "vidu-1"
        self.duration = 4  # 默认4秒视频


class AudioConfig:
    """音频配置"""
    def __init__(self):
        self.api_key = settings.JIANYING_API_KEY
        self.voice_style = "female_young"
        self.speed = 1.0
        self.volume = 0.8


class UploadConfig:
    """上传配置"""
    def __init__(self):
        self.platforms = {
            "douyin": {
                "enabled": True,
                "title_template": "#{keywords} {title}",
                "max_title_length": 55
            },
            "bilibili": {
                "enabled": True,
                "title_template": "{title}",
                "max_title_length": 80
            },
            "xiaohongshu": {
                "enabled": True,
                "title_template": "{title} #{keywords}",
                "max_title_length": 20
            }
        }


# 全局配置实例
settings = Settings()