"""
内容采集模块
基于MediaCrawler进行定制化开发
"""

import asyncio
import uuid
from datetime import datetime
from typing import List, Dict, Optional
from sqlalchemy.orm import Session

from app.core.config import CrawlerConfig, settings
from app.core.database import get_db
from app.core.logger import logger
from app.models import Comment, ContentStatus


class CrawlerModule:
    """内容采集模块"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.platforms = config.platforms
        self.keywords = config.keywords
        self.is_running = False
        
    async def crawl_comments(self, platform: str, keywords: List[str]) -> List[Comment]:
        """爬取指定平台的评论"""
        try:
            logger.info(f"开始爬取 {platform} 平台评论，关键词: {keywords}")
            
            # TODO: 集成MediaCrawler实际爬取逻辑
            # 这里先使用模拟数据进行开发
            mock_comments = await self._mock_crawl_data(platform, keywords)
            
            # 保存到数据库
            saved_comments = []
            db = next(get_db())
            
            try:
                for comment_data in mock_comments:
                    comment = Comment(
                        id=str(uuid.uuid4()),
                        platform=platform,
                        content=comment_data["content"],
                        likes=comment_data["likes"],
                        timestamp=comment_data["timestamp"],
                        keywords=keywords,
                        status=ContentStatus.PENDING.value
                    )
                    
                    db.add(comment)
                    saved_comments.append(comment)
                
                db.commit()
                logger.info(f"成功保存 {len(saved_comments)} 条评论")
                
            except Exception as e:
                db.rollback()
                logger.error(f"保存评论到数据库失败: {e}")
                raise
            finally:
                db.close()
            
            return saved_comments
            
        except Exception as e:
            logger.error(f"爬取评论失败: {e}")
            raise
    
    async def _mock_crawl_data(self, platform: str, keywords: List[str]) -> List[Dict]:
        """模拟爬取数据（开发阶段使用）"""
        mock_data = [
            {
                "content": "昨天晚上回家的路上遇到了一件超级诡异的事情，到现在想起来还是毛骨悚然...",
                "likes": 156,
                "timestamp": datetime.now()
            },
            {
                "content": "我前任真的是个奇葩，分手后居然还要求我把他送的礼物都还回去，包括一支笔...",
                "likes": 89,
                "timestamp": datetime.now()
            },
            {
                "content": "今天在公司电梯里发生了史上最尴尬的社死现场，我现在都不敢出办公室了...",
                "likes": 234,
                "timestamp": datetime.now()
            }
        ]
        
        # 模拟网络延迟
        await asyncio.sleep(1)
        
        return mock_data
    
    async def schedule_crawling(self, interval: int = 3600):
        """定时爬取任务"""
        logger.info(f"启动定时爬取任务，间隔: {interval}秒")
        self.is_running = True
        
        while self.is_running:
            try:
                for platform in self.platforms:
                    await self.crawl_comments(platform, self.keywords)
                
                logger.info(f"等待 {interval} 秒后进行下一轮爬取")
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"定时爬取任务出错: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟再重试
    
    def stop_crawling(self):
        """停止爬取任务"""
        logger.info("停止定时爬取任务")
        self.is_running = False
    
    async def get_crawl_status(self) -> Dict:
        """获取爬取状态"""
        db = next(get_db())
        try:
            total_comments = db.query(Comment).count()
            pending_comments = db.query(Comment).filter(Comment.status == ContentStatus.PENDING.value).count()
            
            platform_stats = {}
            for platform in self.platforms:
                count = db.query(Comment).filter(Comment.platform == platform).count()
                platform_stats[platform] = count
            
            return {
                "is_running": self.is_running,
                "total_comments": total_comments,
                "pending_comments": pending_comments,
                "platform_stats": platform_stats,
                "keywords": self.keywords
            }
        finally:
            db.close()
    
    async def update_config(self, new_keywords: Optional[List[str]] = None, 
                          new_platforms: Optional[List[str]] = None):
        """更新爬取配置"""
        if new_keywords:
            self.keywords = new_keywords
            logger.info(f"更新关键词: {new_keywords}")
        
        if new_platforms:
            self.platforms = new_platforms
            logger.info(f"更新平台列表: {new_platforms}")


# 全局爬虫实例
crawler_module = CrawlerModule(CrawlerConfig())