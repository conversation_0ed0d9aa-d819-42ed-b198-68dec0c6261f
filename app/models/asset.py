"""
生成资产数据模型
"""

from sqlalchemy import Column, String, ForeignKey, Text, JSON
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, AssetType


class GeneratedAsset(BaseModel):
    """生成资产模型"""
    __tablename__ = "generated_assets"
    
    scene_id = Column(String, ForeignKey("scenes.id"), nullable=False, comment="场景ID")
    asset_type = Column(String(20), nullable=False, comment="资产类型")
    file_path = Column(String(500), nullable=False, comment="文件路径")
    api_response = Column(JSON, comment="API响应数据")
    
    # 关联关系
    scene = relationship("Scene", back_populates="assets")
    
    def __repr__(self):
        return f"<GeneratedAsset(id={self.id}, type={self.asset_type}, scene_id={self.scene_id})>"