"""
处理器相关API端点
"""

from typing import Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel

from app.services.processor import processor_module
from app.core.logger import logger

router = APIRouter()


class ProcessRequest(BaseModel):
    """处理请求模型"""
    comment_id: str
    style: Optional[str] = "悬疑惊悚"


class EnhanceRequest(BaseModel):
    """故事增强请求模型"""
    raw_comment: str
    style: Optional[str] = "悬疑惊悚"


@router.post("/process-comment")
async def process_comment(request: ProcessRequest, background_tasks: BackgroundTasks):
    """处理单个评论"""
    try:
        logger.info(f"开始处理评论: {request.comment_id}, 风格: {request.style}")
        
        # 在后台处理评论
        background_tasks.add_task(
            processor_module.process_comment,
            request.comment_id,
            request.style
        )
        
        return {
            "message": "评论处理任务已启动",
            "comment_id": request.comment_id,
            "style": request.style,
            "status": "processing"
        }
    
    except Exception as e:
        logger.error(f"处理评论失败: {e}")
        raise HTTPException(status_code=500, detail="处理评论失败")


@router.post("/enhance-story")
async def enhance_story(request: EnhanceRequest):
    """增强故事"""
    try:
        logger.info(f"开始增强故事，风格: {request.style}")
        
        enhanced_story = await processor_module.enhance_story(
            request.raw_comment,
            request.style
        )
        
        return {
            "message": "故事增强完成",
            "enhanced_story": enhanced_story.to_dict(),
            "word_count": enhanced_story.word_count
        }
    
    except Exception as e:
        logger.error(f"故事增强失败: {e}")
        raise HTTPException(status_code=500, detail="故事增强失败")


@router.post("/generate-scenes/{story_id}")
async def generate_scenes(story_id: str):
    """生成场景分镜"""
    try:
        from sqlalchemy.orm import Session
        from app.core.database import get_db
        from app.models import EnhancedStory
        
        db = next(get_db())
        try:
            story = db.query(EnhancedStory).filter(EnhancedStory.id == story_id).first()
            if not story:
                raise HTTPException(status_code=404, detail="故事不存在")
            
            scenes = await processor_module.generate_scenes(story)
            
            # 保存场景到数据库
            for scene in scenes:
                db.add(scene)
            db.commit()
            
            return {
                "message": "场景生成完成",
                "story_id": story_id,
                "scenes": [scene.to_dict() for scene in scenes],
                "scenes_count": len(scenes)
            }
        finally:
            db.close()
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成场景失败: {e}")
        raise HTTPException(status_code=500, detail="生成场景失败")


@router.get("/styles")
async def get_available_styles():
    """获取可用的故事风格"""
    try:
        styles = list(processor_module.style_templates.keys())
        return {
            "styles": styles,
            "default_style": "悬疑惊悚"
        }
    
    except Exception as e:
        logger.error(f"获取风格列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取风格列表失败")


@router.get("/templates/{style}")
async def get_style_template(style: str):
    """获取指定风格的模板"""
    try:
        if style not in processor_module.style_templates:
            raise HTTPException(status_code=404, detail="风格不存在")
        
        template = processor_module.style_templates[style]
        return {
            "style": style,
            "template": template
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取风格模板失败: {e}")
        raise HTTPException(status_code=500, detail="获取风格模板失败")