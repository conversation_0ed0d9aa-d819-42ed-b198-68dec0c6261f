"""
故事数据模型
"""

from sqlalchemy import Column, String, Text, Integer, ForeignKey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class EnhancedStory(BaseModel):
    """增强故事模型"""
    __tablename__ = "enhanced_stories"
    
    original_comment_id = Column(String, ForeignKey("comments.id"), nullable=False, comment="原始评论ID")
    enhanced_text = Column(Text, nullable=False, comment="增强后的故事文本")
    style = Column(String(50), nullable=False, comment="故事风格")
    word_count = Column(Integer, comment="字数统计")
    
    # 关联关系
    original_comment = relationship("Comment", back_populates="enhanced_stories")
    scenes = relationship("Scene", back_populates="story", cascade="all, delete-orphan")
    final_contents = relationship("FinalContent", back_populates="story")
    
    def __repr__(self):
        return f"<EnhancedStory(id={self.id}, style={self.style}, word_count={self.word_count})>"