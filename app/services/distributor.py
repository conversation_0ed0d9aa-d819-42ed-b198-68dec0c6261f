"""
内容分发服务模块
基于social-auto-upload进行多平台发布
"""

import asyncio
import uuid
from datetime import datetime
from typing import List, Dict, Optional
from sqlalchemy.orm import Session

from app.core.config import UploadConfig, settings
from app.core.database import get_db
from app.core.logger import logger
from app.models import FinalContent, PublishRecord, Platform


class DistributorModule:
    """内容分发模块"""
    
    def __init__(self, upload_config: UploadConfig):
        self.upload_config = upload_config
        self.uploader = None  # TODO: 初始化social-auto-upload
        self.platform_configs = self._load_platform_configs()
        
    def _load_platform_configs(self) -> Dict:
        """加载平台配置"""
        return {
            "douyin": {
                "enabled": True,
                "title_template": "#{keywords} {title}",
                "max_title_length": 55,
                "description_template": "{description} #热门 #推荐",
                "default_tags": ["热门", "推荐", "精彩"]
            },
            "bilibili": {
                "enabled": True,
                "title_template": "{title}",
                "max_title_length": 80,
                "description_template": "{description}",
                "default_tags": ["生活", "故事", "分享"]
            },
            "xiaohongshu": {
                "enabled": True,
                "title_template": "{title} #{keywords}",
                "max_title_length": 20,
                "description_template": "{description} ✨",
                "default_tags": ["生活记录", "故事分享"]
            }
        }
    
    async def publish_to_platform(self, content_id: str, platform: str, 
                                 title: Optional[str] = None, 
                                 description: Optional[str] = None,
                                 tags: Optional[List[str]] = None) -> Dict:
        """发布到指定平台"""
        try:
            db = next(get_db())
            
            try:
                content = db.query(FinalContent).filter(FinalContent.id == content_id).first()
                if not content:
                    raise ValueError(f"内容不存在: {content_id}")
                
                if platform not in self.platform_configs:
                    raise ValueError(f"不支持的平台: {platform}")
                
                platform_config = self.platform_configs[platform]
                if not platform_config["enabled"]:
                    raise ValueError(f"平台未启用: {platform}")
                
                logger.info(f"开始发布到 {platform} - 内容ID: {content_id}")
                
                # 格式化元数据
                formatted_metadata = await self._format_metadata(
                    content, platform, title, description, tags
                )
                
                # TODO: 调用social-auto-upload进行实际发布
                publish_result = await self._mock_publish(content, platform, formatted_metadata)
                
                # 记录发布结果
                record = PublishRecord(
                    id=str(uuid.uuid4()),
                    content_id=content_id,
                    platform=platform,
                    status=publish_result["status"],
                    post_id=publish_result.get("post_id"),
                    post_url=publish_result.get("post_url"),
                    error_message=publish_result.get("error_message"),
                    metadata=formatted_metadata
                )
                
                db.add(record)
                db.commit()
                
                logger.info(f"发布完成 - 平台: {platform}, 状态: {publish_result['status']}")
                
                return {
                    "content_id": content_id,
                    "platform": platform,
                    "status": publish_result["status"],
                    "post_id": publish_result.get("post_id"),
                    "post_url": publish_result.get("post_url"),
                    "record_id": record.id
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"发布到平台失败: {e}")
            raise
    
    async def batch_publish(self, content_id: str, platforms: List[str],
                          auto_generate_metadata: bool = True) -> Dict:
        """批量发布到多平台"""
        try:
            logger.info(f"开始批量发布 - 内容ID: {content_id}, 平台: {platforms}")
            
            results = {}
            successful_platforms = []
            failed_platforms = []
            
            for platform in platforms:
                try:
                    result = await self.publish_to_platform(content_id, platform)
                    results[platform] = result
                    
                    if result["status"] == "success":
                        successful_platforms.append(platform)
                    else:
                        failed_platforms.append(platform)
                        
                except Exception as e:
                    logger.error(f"发布到 {platform} 失败: {e}")
                    results[platform] = {
                        "status": "failed",
                        "error": str(e)
                    }
                    failed_platforms.append(platform)
                
                # 避免API调用过于频繁
                await asyncio.sleep(2)
            
            logger.info(f"批量发布完成 - 成功: {len(successful_platforms)}, 失败: {len(failed_platforms)}")
            
            return {
                "content_id": content_id,
                "total_platforms": len(platforms),
                "successful_platforms": successful_platforms,
                "failed_platforms": failed_platforms,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"批量发布失败: {e}")
            raise
    
    async def _format_metadata(self, content: FinalContent, platform: str,
                             title: Optional[str], description: Optional[str],
                             tags: Optional[List[str]]) -> Dict:
        """格式化平台元数据"""
        try:
            platform_config = self.platform_configs[platform]
            
            # 获取故事信息用于生成标题和描述
            db = next(get_db())
            try:
                from app.models import EnhancedStory
                story = db.query(EnhancedStory).filter(EnhancedStory.id == content.story_id).first()
                
                # 生成默认标题
                if not title and story:
                    title = self._generate_title(story.enhanced_text, story.style)
                
                # 生成默认描述
                if not description and story:
                    description = story.enhanced_text[:100] + "..."
                
                # 使用默认标签
                if not tags:
                    tags = platform_config["default_tags"]
                
                # 格式化标题
                formatted_title = platform_config["title_template"].format(
                    title=title or "精彩故事",
                    keywords=" ".join(tags[:3]) if tags else ""
                )
                
                # 截断标题长度
                if len(formatted_title) > platform_config["max_title_length"]:
                    formatted_title = formatted_title[:platform_config["max_title_length"]-3] + "..."
                
                # 格式化描述
                formatted_description = platform_config.get("description_template", "{description}").format(
                    description=description or ""
                )
                
                return {
                    "title": formatted_title,
                    "description": formatted_description,
                    "tags": tags or [],
                    "video_path": content.video_path,
                    "thumbnail_path": content.thumbnail_path,
                    "duration": content.duration
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"格式化元数据失败: {e}")
            raise
    
    def _generate_title(self, story_text: str, style: str) -> str:
        """生成标题"""
        # 简单的标题生成逻辑
        if "诡异" in story_text or "灵异" in story_text:
            return "深夜遇到的诡异事件"
        elif "前任" in story_text:
            return "前任的奇葩操作"
        elif "社死" in story_text:
            return "史上最尴尬的社死现场"
        else:
            return "真实故事分享"
    
    async def _mock_publish(self, content: FinalContent, platform: str, metadata: Dict) -> Dict:
        """模拟发布（开发阶段使用）"""
        # 模拟发布时间
        await asyncio.sleep(3)
        
        # 模拟发布结果
        import random
        if random.random() > 0.1:  # 90%成功率
            post_id = f"{platform}_{random.randint(100000, 999999)}"
            return {
                "status": "success",
                "post_id": post_id,
                "post_url": f"https://{platform}.com/video/{post_id}"
            }
        else:
            return {
                "status": "failed",
                "error_message": "网络连接超时"
            }
    
    def get_supported_platforms(self) -> List[Dict]:
        """获取支持的平台"""
        platforms = []
        for platform_id, config in self.platform_configs.items():
            platforms.append({
                "id": platform_id,
                "name": self._get_platform_name(platform_id),
                "enabled": config["enabled"],
                "max_title_length": config["max_title_length"]
            })
        return platforms
    
    def _get_platform_name(self, platform_id: str) -> str:
        """获取平台名称"""
        names = {
            "douyin": "抖音",
            "bilibili": "哔哩哔哩",
            "xiaohongshu": "小红书",
            "kuaishou": "快手"
        }
        return names.get(platform_id, platform_id)
    
    async def get_publish_history(self, skip: int = 0, limit: int = 50) -> Dict:
        """获取发布历史"""
        try:
            db = next(get_db())
            try:
                records = db.query(PublishRecord).offset(skip).limit(limit).all()
                total = db.query(PublishRecord).count()
                
                return {
                    "records": [record.to_dict() for record in records],
                    "total": total,
                    "skip": skip,
                    "limit": limit
                }
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取发布历史失败: {e}")
            raise


# 全局分发模块实例
distributor_module = DistributorModule(UploadConfig())
