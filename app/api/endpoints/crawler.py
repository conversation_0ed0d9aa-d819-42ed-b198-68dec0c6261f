"""
爬虫相关API端点
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel

from app.services.crawler import crawler_module
from app.core.logger import logger

router = APIRouter()


class CrawlRequest(BaseModel):
    """爬取请求模型"""
    platforms: Optional[List[str]] = None
    keywords: Optional[List[str]] = None


class ConfigUpdateRequest(BaseModel):
    """配置更新请求模型"""
    keywords: Optional[List[str]] = None
    platforms: Optional[List[str]] = None


@router.post("/start")
async def start_crawling(request: CrawlRequest, background_tasks: BackgroundTasks):
    """启动爬取任务"""
    try:
        platforms = request.platforms or crawler_module.platforms
        keywords = request.keywords or crawler_module.keywords
        
        logger.info(f"启动爬取任务 - 平台: {platforms}, 关键词: {keywords}")
        
        # 在后台启动定时爬取任务
        background_tasks.add_task(crawler_module.schedule_crawling)
        
        return {
            "message": "爬取任务已启动",
            "platforms": platforms,
            "keywords": keywords,
            "status": "started"
        }
    
    except Exception as e:
        logger.error(f"启动爬取任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动爬取任务失败")


@router.post("/stop")
async def stop_crawling():
    """停止爬取任务"""
    try:
        crawler_module.stop_crawling()
        
        return {
            "message": "爬取任务已停止",
            "status": "stopped"
        }
    
    except Exception as e:
        logger.error(f"停止爬取任务失败: {e}")
        raise HTTPException(status_code=500, detail="停止爬取任务失败")


@router.get("/status")
async def get_crawl_status():
    """获取爬取状态"""
    try:
        status = await crawler_module.get_crawl_status()
        return status
    
    except Exception as e:
        logger.error(f"获取爬取状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取爬取状态失败")


@router.post("/crawl-once")
async def crawl_once(request: CrawlRequest):
    """执行一次性爬取"""
    try:
        platforms = request.platforms or crawler_module.platforms
        keywords = request.keywords or crawler_module.keywords
        
        results = {}
        for platform in platforms:
            comments = await crawler_module.crawl_comments(platform, keywords)
            results[platform] = len(comments)
        
        return {
            "message": "一次性爬取完成",
            "results": results,
            "total_comments": sum(results.values())
        }
    
    except Exception as e:
        logger.error(f"一次性爬取失败: {e}")
        raise HTTPException(status_code=500, detail="一次性爬取失败")


@router.put("/config")
async def update_config(request: ConfigUpdateRequest):
    """更新爬取配置"""
    try:
        await crawler_module.update_config(
            new_keywords=request.keywords,
            new_platforms=request.platforms
        )
        
        return {
            "message": "配置更新成功",
            "keywords": crawler_module.keywords,
            "platforms": crawler_module.platforms
        }
    
    except Exception as e:
        logger.error(f"更新配置失败: {e}")
        raise HTTPException(status_code=500, detail="更新配置失败")