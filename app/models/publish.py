"""
发布记录数据模型
"""

from sqlalchemy import Column, String, Text, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class PublishRecord(BaseModel):
    """发布记录模型"""
    __tablename__ = "publish_records"
    
    content_id = Column(String, ForeignKey("final_contents.id"), nullable=False, comment="内容ID")
    platform = Column(String(50), nullable=False, comment="发布平台")
    post_id = Column(String, comment="平台内容ID")
    post_url = Column(String(500), comment="发布链接")
    status = Column(String(20), default="pending", comment="发布状态")
    published_at = Column(DateTime, comment="发布时间")
    error_message = Column(Text, comment="错误信息")
    metadata = Column(JSON, comment="发布元数据")
    
    # 关联关系
    content = relationship("FinalContent", back_populates="publish_records")
    
    def __repr__(self):
        return f"<PublishRecord(id={self.id}, platform={self.platform}, status={self.status})>"