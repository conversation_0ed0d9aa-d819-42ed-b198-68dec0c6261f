"""
API路由配置
"""

from fastapi import APIRouter
from app.api.endpoints import comments, stories, crawler, processor, visuals, synthesizer, distributor

api_router = APIRouter()

# 注册各模块路由
api_router.include_router(comments.router, prefix="/comments", tags=["comments"])
api_router.include_router(stories.router, prefix="/stories", tags=["stories"])
api_router.include_router(crawler.router, prefix="/crawler", tags=["crawler"])
api_router.include_router(processor.router, prefix="/processor", tags=["processor"])
api_router.include_router(visuals.router, prefix="/visuals", tags=["visuals"])
api_router.include_router(synthesizer.router, prefix="/synthesizer", tags=["synthesizer"])
api_router.include_router(distributor.router, prefix="/distributor", tags=["distributor"])