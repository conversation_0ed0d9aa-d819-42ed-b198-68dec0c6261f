"""
发布记录数据模型
"""

from sqlalchemy import Column, String, Text, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class PublishRecord(BaseModel):
    """发布记录模型"""
    __tablename__ = "publish_records"
    
    content_id = Column(String, ForeignKey("final_contents.id"), nullable=False, comment="内容ID")
    platform = Column(String(50), nullable=False, comment="发布平台")
    platform_id = Column(String, comment="平台内容ID")
    title = Column(String(200), nullable=False, comment="标题")
    description = Column(Text, comment="描述")
    tags = Column(JSON, comment="标签列表")
    status = Column(String(20), default="pending", comment="发布状态")
    published_at = Column(DateTime, comment="发布时间")
    error_message = Column(Text, comment="错误信息")
    
    # 关联关系
    content = relationship("FinalContent", back_populates="publish_records")
    
    def __repr__(self):
        return f"<PublishRecord(id={self.id}, platform={self.platform}, status={self.status})>"