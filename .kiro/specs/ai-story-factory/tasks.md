# 实施计划

## 自动化程度说明

### 完全自动化的流程
- **内容采集**: MediaCrawler定时自动爬取，关键词过滤，数据入库
- **故事增强**: DeepSeek LLM自动润色和分镜，无需人工干预
- **视觉生成**: 豆包API自动生成关键帧图片
- **视频合成**: Vidu API自动生成短片段，FFmpeg自动拼接成完整视频
- **音频处理**: AI自动配音，背景音乐和音效自动添加
- **多平台发布**: social-auto-upload自动发布到各平台

### 需要人工干预的环节
- **初始配置**: API密钥配置、平台认证信息设置（一次性）
- **质量监控**: 定期检查生成内容质量，调整提示词模板
- **异常处理**: 当API调用失败或生成质量异常时的人工审核
- **内容审核**: 可选的发布前人工审核机制（可配置开启/关闭）
- **系统维护**: 日志清理、数据库维护、性能优化

### Vidu视频时长限制解决方案
1. **智能分镜**: 将长故事自动分解为3-5个短场景（每个4-6秒）
2. **片段拼接**: 使用FFmpeg自动拼接多个短片段成完整视频
3. **时长平衡**: 自动调整片段播放速度以匹配旁白时长
4. **循环延长**: 对于较短的视频片段，智能循环播放以匹配音频
5. **转场效果**: 添加淡入淡出等转场，确保片段间平滑过渡

- [ ] 1. 搭建项目基础架构和核心接口
  - 创建Python项目结构，包含models、services、api、utils等目录
  - 实现核心数据模型类和枚举定义
  - 设置FastAPI应用和基础路由结构
  - 配置SQLite数据库连接和表结构创建
  - _需求: 需求7.1, 需求7.2_

- [ ] 2. 实现数据存储层和基础工具
  - [ ] 2.1 创建数据库模型和ORM映射
    - 实现Comment、EnhancedStory、Scene等数据模型类
    - 创建数据库表结构和索引
    - 编写数据访问层(DAO)基础类和CRUD操作
    - _需求: 需求1.3, 需求1.4_

  - [ ] 2.2 实现配置管理和日志系统
    - 创建配置文件加载和验证机制
    - 实现StoryForgeLogger类和日志处理器
    - 添加错误分类和异常处理基础类
    - 编写重试装饰器和速率限制工具
    - _需求: 需求6.1, 需求6.2, 需求6.3_

- [ ] 3. 集成MediaCrawler实现内容采集模块
  - [ ] 3.1 配置和定制MediaCrawler
    - 克隆MediaCrawler项目并进行必要的定制化修改
    - 配置抖音、B站、小红书的爬取参数
    - 实现关键词过滤和评论筛选逻辑
    - 编写爬取结果的数据清洗和标准化处理
    - _需求: 需求1.1, 需求1.2_

  - [ ] 3.2 实现CrawlerModule类和API接口
    - 创建CrawlerModule类，封装MediaCrawler功能
    - 实现定时爬取任务调度机制
    - 编写爬取状态监控和错误处理逻辑
    - 创建爬虫管理的REST API接口
    - _需求: 需求1.5, 需求6.4_

- [ ] 4. 实现DeepSeek LLM集成和智能处理模块
  - [ ] 4.1 配置DeepSeek LLM本地部署
    - 设置DeepSeek LLM的本地部署环境
    - 创建LLM客户端封装类和连接管理
    - 实现提示词模板系统和风格配置
    - 编写LLM调用的错误处理和重试机制
    - _需求: 需求2.1, 需求2.6_

  - [ ] 4.2 实现故事增强和分镜生成功能
    - 创建ProcessorModule类和故事增强方法
    - 实现智能分镜算法和场景分解逻辑
    - 编写图片和视频提示词生成功能
    - 添加风格参数控制和模板定制功能
    - _需求: 需求2.2, 需求2.3, 需求2.4, 需求2.5_

- [ ] 5. 集成外部API实现视觉素材生成
  - [ ] 5.1 实现豆包API集成
    - 创建豆包API客户端和认证管理
    - 实现图片生成请求和响应处理
    - 添加图片缓存机制和本地存储管理
    - 编写批量生成和并发控制逻辑
    - _需求: 需求3.1, 需求3.3_

  - [ ] 5.2 创建VisualsModule和图片处理功能
    - 实现VisualsModule类和关键帧生成方法
    - 添加图片质量检查和格式转换功能
    - 创建图片与场景的关联和元数据管理
    - 实现图片生成失败的重试和替代策略
    - _需求: 需求3.2, 需求3.4_

- [ ] 6. 实现视频和音频合成模块
  - [ ] 6.1 集成Vidu API进行视频片段生成
    - 创建Vidu API客户端，处理单个场景的短视频生成（通常4-6秒）
    - 实现关键帧图片到视频片段的转换，每个场景生成独立片段
    - 添加视频片段的质量检查和格式标准化（统一分辨率、帧率）
    - 编写视频生成的进度跟踪和批量处理管理
    - 实现视频片段的自动命名和序列管理（scene_01.mp4, scene_02.mp4等）
    - _需求: 需求4.1, 需求4.7_

  - [ ] 6.2 实现智能视频拼接和时长管理
    - 开发故事时长计算算法，根据文本长度和语速估算总时长
    - 实现视频片段的智能拼接，确保场景间的平滑过渡
    - 添加转场效果（淡入淡出、交叉溶解）来连接不同场景
    - 创建视频时长平衡算法，自动调整片段播放速度以匹配旁白
    - 实现视频片段的循环播放或延长功能，以匹配较长的旁白段落
    - _需求: 需求4.1, 需求4.2_

  - [ ] 6.3 实现音频生成和同步功能
    - 集成剪映API或ElevenLabs进行AI配音，生成完整故事旁白
    - 实现旁白文本的智能分段，与视频场景进行时间对齐
    - 添加背景音乐(BGM)的自动选择和音量调节
    - 创建音效(SFX)的智能添加，根据故事内容自动匹配合适音效
    - 实现音频轨道的混合和最终音视频同步
    - _需求: 需求4.3, 需求4.4, 需求4.5_

  - [ ] 6.4 创建最终视频导出和优化
    - 实现多种视频格式的导出（抖音竖屏、B站横屏等）
    - 添加平台特定的视频优化（码率、分辨率、时长限制）
    - 创建视频质量检查和自动修复功能
    - 实现视频文件的压缩和存储管理
    - _需求: 需求4.6, 需求5.2_

- [ ] 7. 集成social-auto-upload实现多平台分发
  - [ ] 7.1 配置social-auto-upload项目
    - 克隆social-auto-upload项目并进行集成配置
    - 设置各平台(抖音、B站、小红书)的认证信息
    - 实现平台特定的内容格式化和元数据处理
    - 编写发布状态跟踪和结果反馈机制
    - _需求: 需求5.1, 需求5.2_

  - [ ] 7.2 实现DistributorModule和发布管理
    - 创建DistributorModule类和批量发布功能
    - 实现发布队列管理和并发控制
    - 添加发布失败的重试和错误记录机制
    - 创建发布历史和统计分析功能
    - _需求: 需求5.3, 需求5.4, 需求5.5_

- [ ] 8. 实现任务调度和工作流编排
  - [ ] 8.1 配置Celery任务队列系统
    - 设置Redis作为消息代理和结果后端
    - 创建Celery应用配置和任务定义
    - 实现各模块的异步任务封装
    - 添加任务监控和失败处理机制
    - _需求: 需求6.4, 需求6.5_

  - [ ] 8.2 实现端到端工作流编排
    - 创建主工作流控制器和任务链管理
    - 实现任务状态跟踪和进度报告
    - 添加工作流暂停、恢复和取消功能
    - 编写工作流性能监控和优化逻辑
    - _需求: 需求7.4, 需求7.5_

- [ ] 9. 创建管理界面和API接口
  - [ ] 9.1 实现REST API接口
    - 创建内容管理的CRUD API接口
    - 实现系统状态监控和统计API
    - 添加配置管理和系统控制API
    - 编写API文档和接口测试用例
    - _需求: 需求7.1, 需求7.3_

  - [ ] 9.2 开发Web管理界面
    - 创建基于FastAPI的静态文件服务
    - 实现内容列表、详情和编辑页面
    - 添加系统监控仪表板和日志查看
    - 创建配置管理和任务控制界面
    - _需求: 需求6.1, 需求6.2_

- [ ] 10. 编写测试用例和质量保证
  - [ ] 10.1 实现单元测试
    - 为每个模块编写单元测试用例
    - 创建Mock对象和测试数据生成器
    - 实现API接口的自动化测试
    - 添加代码覆盖率检查和质量门禁
    - _需求: 需求6.3, 需求7.2_

  - [ ] 10.2 实现集成测试和性能测试
    - 编写端到端工作流的集成测试
    - 创建并发处理和负载测试用例
    - 实现API速率限制和错误恢复测试
    - 添加性能基准测试和监控报告
    - _需求: 需求6.4, 需求6.5_

- [ ] 11. 系统部署和运维配置
  - [ ] 11.1 创建部署脚本和配置
    - 编写Docker容器化配置文件
    - 创建系统启动和停止脚本
    - 实现数据库迁移和初始化脚本
    - 添加系统健康检查和自动重启机制
    - _需求: 需求7.1, 需求7.5_

  - [ ] 11.2 实现监控和运维工具
    - 配置系统性能监控和告警机制
    - 创建日志轮转和清理自动化脚本
    - 实现数据备份和恢复流程
    - 编写运维手册和故障排查指南
    - _需求: 需求6.1, 需求6.2_

- [ ] 12. 系统优化和扩展功能
  - [ ] 12.1 性能优化和资源管理
    - 优化数据库查询和索引设计
    - 实现缓存策略和内存管理优化
    - 添加API调用成本控制和预算管理
    - 创建系统资源使用监控和优化建议
    - _需求: 需求6.4, 需求6.5_

  - [ ] 12.2 实现扩展功能和未来特性
    - 添加内容质量评估和自动筛选功能
    - 实现用户反馈收集和内容优化机制
    - 创建A/B测试框架用于内容效果对比
    - 预留音频播客和图文内容生成的接口扩展
    - _需求: 需求7.3, 需求7.4_