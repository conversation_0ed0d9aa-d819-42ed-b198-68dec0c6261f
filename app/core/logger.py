"""
日志系统模块
"""

import os
import sys
from datetime import datetime
from typing import Optional
from loguru import logger as loguru_logger

from app.core.config import settings


class StoryForgeLogger:
    """StoryForge日志管理器"""
    
    def __init__(self):
        self.setup_logger()
    
    def setup_logger(self):
        """配置日志系统"""
        # 移除默认处理器
        loguru_logger.remove()
        
        # 确保日志目录存在
        log_dir = os.path.dirname(settings.LOG_FILE)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 控制台输出
        loguru_logger.add(
            sys.stdout,
            level=settings.LOG_LEVEL,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            colorize=True
        )
        
        # 文件输出
        loguru_logger.add(
            settings.LOG_FILE,
            level=settings.LOG_LEVEL,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="10 MB",
            retention="30 days",
            compression="zip"
        )
        
        # 错误日志单独文件
        error_log_file = settings.LOG_FILE.replace('.log', '_error.log')
        loguru_logger.add(
            error_log_file,
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="10 MB",
            retention="30 days",
            compression="zip"
        )
    
    def log_processing_step(self, step: str, content_id: str, status: str, details: Optional[str] = None):
        """记录处理步骤"""
        message = f"Step: {step} | Content: {content_id} | Status: {status}"
        if details:
            message += f" | Details: {details}"
        loguru_logger.info(message)
    
    def log_api_call(self, service: str, endpoint: str, response_time: float, 
                     status: str, details: Optional[str] = None):
        """记录API调用"""
        message = f"API: {service}/{endpoint} | Time: {response_time:.2f}s | Status: {status}"
        if details:
            message += f" | Details: {details}"
        loguru_logger.info(message)
    
    def log_error(self, error: Exception, context: Optional[str] = None):
        """记录错误"""
        message = f"Error: {str(error)}"
        if context:
            message = f"{context} | {message}"
        loguru_logger.error(message)
        loguru_logger.exception("Exception details:")
    
    def log_workflow_start(self, workflow_id: str, workflow_type: str):
        """记录工作流开始"""
        loguru_logger.info(f"Workflow Started | ID: {workflow_id} | Type: {workflow_type}")
    
    def log_workflow_complete(self, workflow_id: str, duration: float, status: str):
        """记录工作流完成"""
        loguru_logger.info(f"Workflow Complete | ID: {workflow_id} | Duration: {duration:.2f}s | Status: {status}")


# 全局日志实例
story_logger = StoryForgeLogger()
logger = loguru_logger