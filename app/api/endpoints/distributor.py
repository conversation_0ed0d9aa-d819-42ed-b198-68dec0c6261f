"""
内容分发相关API端点
"""

from typing import List, Optional, Dict
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel

from app.core.logger import logger

router = APIRouter()


class PublishRequest(BaseModel):
    """发布请求模型"""
    content_id: str
    platforms: List[str]
    title: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    schedule_time: Optional[str] = None  # ISO格式时间字符串


class PlatformConfig(BaseModel):
    """平台配置模型"""
    platform: str
    enabled: bool
    title_template: str
    max_title_length: int
    description_template: Optional[str] = None
    default_tags: Optional[List[str]] = None


class BatchPublishRequest(BaseModel):
    """批量发布请求模型"""
    content_ids: List[str]
    platforms: List[str]
    auto_generate_metadata: Optional[bool] = True


@router.post("/publish")
async def publish_content(request: PublishRequest, background_tasks: BackgroundTasks):
    """发布内容到指定平台"""
    try:
        from sqlalchemy.orm import Session
        from app.core.database import get_db
        from app.models import FinalContent
        
        db = next(get_db())
        try:
            content = db.query(FinalContent).filter(FinalContent.id == request.content_id).first()
            if not content:
                raise HTTPException(status_code=404, detail="内容不存在")
            
            logger.info(f"开始发布内容 - ID: {request.content_id}, 平台: {request.platforms}")
            
            # 在后台处理发布任务
            background_tasks.add_task(
                _process_content_publishing,
                request.content_id,
                request.platforms,
                request.title,
                request.description,
                request.tags,
                request.schedule_time
            )
            
            return {
                "message": "内容发布任务已启动",
                "content_id": request.content_id,
                "platforms": request.platforms,
                "status": "publishing"
            }
        finally:
            db.close()
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发布内容失败: {e}")
        raise HTTPException(status_code=500, detail="发布内容失败")


@router.post("/batch-publish")
async def batch_publish_content(request: BatchPublishRequest, background_tasks: BackgroundTasks):
    """批量发布内容"""
    try:
        logger.info(f"开始批量发布 - 内容数: {len(request.content_ids)}, 平台: {request.platforms}")
        
        # 在后台处理批量发布任务
        background_tasks.add_task(
            _process_batch_publishing,
            request.content_ids,
            request.platforms,
            request.auto_generate_metadata
        )
        
        return {
            "message": "批量发布任务已启动",
            "content_count": len(request.content_ids),
            "platforms": request.platforms,
            "status": "processing"
        }
    
    except Exception as e:
        logger.error(f"批量发布失败: {e}")
        raise HTTPException(status_code=500, detail="批量发布失败")


@router.get("/platforms")
async def get_supported_platforms():
    """获取支持的发布平台"""
    try:
        platforms = [
            {
                "id": "douyin",
                "name": "抖音",
                "description": "字节跳动旗下短视频平台",
                "supported_formats": ["mp4"],
                "max_duration": 180,
                "resolution": "1080x1920",
                "enabled": True
            },
            {
                "id": "bilibili",
                "name": "哔哩哔哩",
                "description": "中国领先的年轻人文化社区",
                "supported_formats": ["mp4", "flv"],
                "max_duration": 600,
                "resolution": "1920x1080",
                "enabled": True
            },
            {
                "id": "xiaohongshu",
                "name": "小红书",
                "description": "生活方式分享平台",
                "supported_formats": ["mp4"],
                "max_duration": 300,
                "resolution": "1080x1920",
                "enabled": True
            },
            {
                "id": "kuaishou",
                "name": "快手",
                "description": "短视频社交平台",
                "supported_formats": ["mp4"],
                "max_duration": 180,
                "resolution": "1080x1920",
                "enabled": False
            }
        ]
        
        return {
            "platforms": platforms,
            "total_count": len(platforms),
            "enabled_count": len([p for p in platforms if p["enabled"]])
        }
    
    except Exception as e:
        logger.error(f"获取平台列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取平台列表失败")


@router.get("/platform-config/{platform}")
async def get_platform_config(platform: str):
    """获取平台配置"""
    try:
        # TODO: 从配置文件或数据库加载实际配置
        configs = {
            "douyin": {
                "platform": "douyin",
                "enabled": True,
                "title_template": "#{keywords} {title}",
                "max_title_length": 55,
                "description_template": "{description} #热门 #推荐",
                "default_tags": ["热门", "推荐", "精彩"]
            },
            "bilibili": {
                "platform": "bilibili",
                "enabled": True,
                "title_template": "{title}",
                "max_title_length": 80,
                "description_template": "{description}",
                "default_tags": ["生活", "故事", "分享"]
            },
            "xiaohongshu": {
                "platform": "xiaohongshu",
                "enabled": True,
                "title_template": "{title} #{keywords}",
                "max_title_length": 20,
                "description_template": "{description} ✨",
                "default_tags": ["生活记录", "故事分享"]
            }
        }
        
        if platform not in configs:
            raise HTTPException(status_code=404, detail="平台配置不存在")
        
        return configs[platform]
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取平台配置失败: {e}")
        raise HTTPException(status_code=500, detail="获取平台配置失败")


@router.put("/platform-config/{platform}")
async def update_platform_config(platform: str, config: PlatformConfig):
    """更新平台配置"""
    try:
        logger.info(f"更新平台配置 - 平台: {platform}")
        
        # TODO: 保存配置到数据库或配置文件
        
        return {
            "message": "平台配置更新成功",
            "platform": platform,
            "config": config.dict()
        }
    
    except Exception as e:
        logger.error(f"更新平台配置失败: {e}")
        raise HTTPException(status_code=500, detail="更新平台配置失败")


@router.get("/publish-status/{task_id}")
async def get_publish_status(task_id: str):
    """获取发布状态"""
    try:
        # TODO: 实现实际的任务状态查询
        mock_status = {
            "task_id": task_id,
            "status": "completed",
            "total_platforms": 3,
            "successful_platforms": 2,
            "failed_platforms": 1,
            "results": {
                "douyin": {"status": "success", "post_id": "dy123456", "url": "https://douyin.com/video/dy123456"},
                "bilibili": {"status": "success", "post_id": "BV1234567890", "url": "https://bilibili.com/video/BV1234567890"},
                "xiaohongshu": {"status": "failed", "error": "标题长度超限"}
            }
        }
        
        return mock_status
    
    except Exception as e:
        logger.error(f"获取发布状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取发布状态失败")


@router.get("/publish-history")
async def get_publish_history(skip: int = 0, limit: int = 50):
    """获取发布历史"""
    try:
        from sqlalchemy.orm import Session
        from app.core.database import get_db
        from app.models import PublishRecord
        
        db = next(get_db())
        try:
            records = db.query(PublishRecord).offset(skip).limit(limit).all()
            total = db.query(PublishRecord).count()
            
            return {
                "records": [record.to_dict() for record in records],
                "total": total,
                "skip": skip,
                "limit": limit
            }
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"获取发布历史失败: {e}")
        raise HTTPException(status_code=500, detail="获取发布历史失败")


async def _process_content_publishing(content_id: str, platforms: List[str], 
                                    title: Optional[str], description: Optional[str],
                                    tags: Optional[List[str]], schedule_time: Optional[str]):
    """处理内容发布的后台任务"""
    try:
        logger.info(f"开始处理发布任务 - 内容ID: {content_id}")
        
        # TODO: 实现实际的发布逻辑
        # 1. 获取内容文件
        # 2. 为每个平台格式化元数据
        # 3. 调用social-auto-upload进行发布
        # 4. 记录发布结果
        
        import asyncio
        for platform in platforms:
            logger.info(f"发布到 {platform}")
            await asyncio.sleep(5)  # 模拟发布时间
        
        logger.info(f"发布完成 - 内容ID: {content_id}")
        
    except Exception as e:
        logger.error(f"发布任务失败: {e}")


async def _process_batch_publishing(content_ids: List[str], platforms: List[str], 
                                  auto_generate_metadata: bool):
    """处理批量发布的后台任务"""
    try:
        logger.info(f"开始处理批量发布任务 - {len(content_ids)} 个内容")
        
        # TODO: 实现实际的批量发布逻辑
        import asyncio
        for content_id in content_ids:
            await _process_content_publishing(content_id, platforms, None, None, None, None)
        
        logger.info("批量发布完成")
        
    except Exception as e:
        logger.error(f"批量发布任务失败: {e}")
