# 设计文档

## 概述

StoryForge（故事工厂）是一个基于现有开源项目构建的AI自动化内容生产系统。系统利用MediaCrawler进行内容采集，使用DeepSeek LLM进行故事增强和分镜，集成多个AI服务进行视觉和视频生成，最后通过social-auto-upload实现多平台分发。

## 系统架构

### 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   内容采集模块    │───▶│   智能处理模块    │───▶│  视觉素材生成模块  │
│  (MediaCrawler) │    │  (DeepSeek LLM) │    │   (豆包 API)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据存储层     │    │   任务调度器     │    │   内容合成模块    │
│   (SQLite)     │    │   (Celery)     │    │   (Vidu API)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   监控日志系统    │    │   内容分发模块    │
                       │   (Logging)    │    │(social-auto-upload)│
                       └─────────────────┘    └─────────────────┘
```

### 技术栈选择

- **编程语言**: Python 3.9+
- **Web框架**: FastAPI (用于API接口和管理界面)
- **数据库**: SQLite (轻量级，适合单机部署)
- **任务队列**: Celery + Redis (异步任务处理)
- **AI服务**: 
  - DeepSeek LLM (本地部署，故事增强)
  - 豆包API (图片生成)
  - Vidu API (视频生成)
  - 剪映API (语音合成)
- **媒体处理**: FFmpeg, MoviePy
- **开源集成**:
  - MediaCrawler (内容爬取)
  - social-auto-upload (多平台发布)

## 组件和接口

### 1. 内容采集模块 (CrawlerModule)

基于MediaCrawler项目进行定制化开发。

```python
class CrawlerModule:
    def __init__(self, config: CrawlerConfig):
        self.platforms = ['douyin', 'bilibili', 'xiaohongshu']
        self.keywords = config.keywords
        self.crawler = MediaCrawler(config)
    
    async def crawl_comments(self, platform: str, keywords: List[str]) -> List[Comment]:
        """爬取指定平台的评论"""
        pass
    
    async def schedule_crawling(self, interval: int = 3600):
        """定时爬取任务"""
        pass
```

**接口设计**:
- `POST /api/crawler/start` - 启动爬取任务
- `GET /api/crawler/status` - 获取爬取状态
- `POST /api/crawler/config` - 更新爬取配置

### 2. 智能处理模块 (ProcessorModule)

集成DeepSeek LLM进行故事增强和分镜生成。

```python
class ProcessorModule:
    def __init__(self, deepseek_config: DeepSeekConfig):
        self.llm = DeepSeekLLM(deepseek_config)
        self.style_templates = self._load_style_templates()
    
    async def enhance_story(self, raw_comment: str, style: str) -> EnhancedStory:
        """故事增强和润色"""
        pass
    
    async def generate_scenes(self, story: EnhancedStory) -> List[Scene]:
        """智能分镜生成"""
        pass
    
    async def create_prompts(self, scene: Scene) -> ScenePrompts:
        """生成图片和视频提示词"""
        pass
```

**提示词模板系统**:
```python
STORY_ENHANCEMENT_TEMPLATE = """
请将以下网友评论改写成一个引人入胜的故事，保持真实性的同时增强叙事性：

原始评论: {raw_comment}
风格要求: {style}
字数要求: 200-500字

要求：
1. 保持故事的真实感
2. 增加情节张力和悬念
3. 丰富人物描述和环境描写
4. 确保故事结构完整
"""

SCENE_BREAKDOWN_TEMPLATE = """
请将以下故事分解为3-5个视觉场景，每个场景包含图片提示词和视频提示词：

故事内容: {enhanced_story}

输出格式：
场景1:
- 图片提示词: [描述静态画面构图、风格、人物、光影]
- 视频提示词: [描述动态动作、运镜、情绪变化]
- 时长: [建议时长]
"""
```

### 3. 视觉素材生成模块 (VisualsModule)

集成豆包API进行图片生成。

```python
class VisualsModule:
    def __init__(self, doubao_config: DoubaoConfig):
        self.api_client = DoubaoAPIClient(doubao_config)
        self.image_cache = ImageCache()
    
    async def generate_keyframe(self, image_prompt: str, style: str) -> Image:
        """生成关键帧图片"""
        pass
    
    async def batch_generate(self, prompts: List[str]) -> List[Image]:
        """批量生成图片"""
        pass
```

### 4. 内容合成模块 (SynthesizerModule)

集成Vidu API和音频处理服务。

```python
class SynthesizerModule:
    def __init__(self, vidu_config: ViduConfig, audio_config: AudioConfig):
        self.vidu_client = ViduAPIClient(vidu_config)
        self.audio_client = AudioAPIClient(audio_config)
        self.ffmpeg = FFmpegProcessor()
    
    async def generate_video_segment(self, keyframe: Image, video_prompt: str) -> VideoSegment:
        """生成视频片段"""
        pass
    
    async def generate_narration(self, story_text: str, voice_style: str) -> AudioTrack:
        """生成AI旁白"""
        pass
    
    async def compose_final_video(self, segments: List[VideoSegment], 
                                 narration: AudioTrack, bgm: AudioTrack) -> FinalVideo:
        """合成最终视频"""
        pass
```

### 5. 内容分发模块 (DistributorModule)

基于social-auto-upload项目进行集成。

```python
class DistributorModule:
    def __init__(self, upload_config: UploadConfig):
        self.uploader = SocialAutoUpload(upload_config)
        self.platform_configs = self._load_platform_configs()
    
    async def publish_to_platform(self, content: FinalVideo, platform: str) -> PublishResult:
        """发布到指定平台"""
        pass
    
    async def batch_publish(self, content: FinalVideo) -> Dict[str, PublishResult]:
        """批量发布到多平台"""
        pass
```

## 数据模型

### 核心数据结构

```python
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import List, Optional

class ContentStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    ENHANCED = "enhanced"
    VISUAL_GENERATED = "visual_generated"
    VIDEO_SYNTHESIZED = "video_synthesized"
    PUBLISHED = "published"
    FAILED = "failed"

@dataclass
class Comment:
    id: str
    platform: str
    content: str
    likes: int
    timestamp: datetime
    keywords: List[str]
    status: ContentStatus
    
@dataclass
class EnhancedStory:
    id: str
    original_comment_id: str
    enhanced_text: str
    style: str
    word_count: int
    created_at: datetime

@dataclass
class Scene:
    id: str
    story_id: str
    sequence: int
    image_prompt: str
    video_prompt: str
    duration: float
    
@dataclass
class GeneratedAsset:
    id: str
    scene_id: str
    asset_type: str  # 'image', 'video', 'audio'
    file_path: str
    api_response: dict
    created_at: datetime

@dataclass
class FinalContent:
    id: str
    story_id: str
    video_path: str
    thumbnail_path: str
    duration: float
    file_size: int
    created_at: datetime

@dataclass
class PublishRecord:
    id: str
    content_id: str
    platform: str
    platform_id: Optional[str]
    title: str
    description: str
    tags: List[str]
    status: str
    published_at: Optional[datetime]
    error_message: Optional[str]
```

### 数据库设计

```sql
-- 评论表
CREATE TABLE comments (
    id TEXT PRIMARY KEY,
    platform TEXT NOT NULL,
    content TEXT NOT NULL,
    likes INTEGER DEFAULT 0,
    timestamp DATETIME NOT NULL,
    keywords TEXT, -- JSON格式存储
    status TEXT DEFAULT 'pending',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 增强故事表
CREATE TABLE enhanced_stories (
    id TEXT PRIMARY KEY,
    original_comment_id TEXT NOT NULL,
    enhanced_text TEXT NOT NULL,
    style TEXT NOT NULL,
    word_count INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (original_comment_id) REFERENCES comments(id)
);

-- 场景表
CREATE TABLE scenes (
    id TEXT PRIMARY KEY,
    story_id TEXT NOT NULL,
    sequence INTEGER NOT NULL,
    image_prompt TEXT NOT NULL,
    video_prompt TEXT NOT NULL,
    duration REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (story_id) REFERENCES enhanced_stories(id)
);

-- 生成资产表
CREATE TABLE generated_assets (
    id TEXT PRIMARY KEY,
    scene_id TEXT NOT NULL,
    asset_type TEXT NOT NULL,
    file_path TEXT NOT NULL,
    api_response TEXT, -- JSON格式存储API响应
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (scene_id) REFERENCES scenes(id)
);

-- 最终内容表
CREATE TABLE final_contents (
    id TEXT PRIMARY KEY,
    story_id TEXT NOT NULL,
    video_path TEXT NOT NULL,
    thumbnail_path TEXT,
    duration REAL,
    file_size INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (story_id) REFERENCES enhanced_stories(id)
);

-- 发布记录表
CREATE TABLE publish_records (
    id TEXT PRIMARY KEY,
    content_id TEXT NOT NULL,
    platform TEXT NOT NULL,
    platform_id TEXT,
    title TEXT NOT NULL,
    description TEXT,
    tags TEXT, -- JSON格式存储
    status TEXT DEFAULT 'pending',
    published_at DATETIME,
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (content_id) REFERENCES final_contents(id)
);
```

## 错误处理

### 错误分类和处理策略

```python
class StoryForgeException(Exception):
    """基础异常类"""
    pass

class CrawlerException(StoryForgeException):
    """爬虫相关异常"""
    pass

class AIServiceException(StoryForgeException):
    """AI服务异常"""
    pass

class MediaProcessingException(StoryForgeException):
    """媒体处理异常"""
    pass

class PublishException(StoryForgeException):
    """发布异常"""
    pass

# 重试装饰器
def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise
                    await asyncio.sleep(delay * (2 ** attempt))
            return None
        return wrapper
    return decorator
```

### 监控和日志系统

```python
import logging
from datetime import datetime

class StoryForgeLogger:
    def __init__(self):
        self.logger = logging.getLogger('storyforge')
        self.setup_handlers()
    
    def setup_handlers(self):
        # 文件处理器
        file_handler = logging.FileHandler('logs/storyforge.log')
        file_handler.setLevel(logging.INFO)
        
        # 错误文件处理器
        error_handler = logging.FileHandler('logs/errors.log')
        error_handler.setLevel(logging.ERROR)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        file_handler.setFormatter(formatter)
        error_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
    
    def log_processing_step(self, step: str, content_id: str, status: str):
        self.logger.info(f"Step: {step}, Content: {content_id}, Status: {status}")
    
    def log_api_call(self, service: str, endpoint: str, response_time: float, status: str):
        self.logger.info(f"API: {service}/{endpoint}, Time: {response_time}s, Status: {status}")
```

## 测试策略

### 单元测试

```python
import pytest
from unittest.mock import Mock, patch

class TestProcessorModule:
    @pytest.fixture
    def processor(self):
        config = Mock()
        return ProcessorModule(config)
    
    @patch('processor.DeepSeekLLM')
    async def test_enhance_story(self, mock_llm, processor):
        # 测试故事增强功能
        mock_llm.return_value.generate.return_value = "增强后的故事"
        
        result = await processor.enhance_story("原始评论", "悬疑")
        
        assert result.enhanced_text == "增强后的故事"
        assert result.style == "悬疑"
    
    async def test_generate_scenes(self, processor):
        # 测试分镜生成功能
        story = Mock()
        story.enhanced_text = "测试故事内容"
        
        scenes = await processor.generate_scenes(story)
        
        assert len(scenes) >= 3
        assert len(scenes) <= 5
        assert all(scene.image_prompt for scene in scenes)
        assert all(scene.video_prompt for scene in scenes)
```

### 集成测试

```python
class TestEndToEndWorkflow:
    async def test_complete_workflow(self):
        # 测试完整的内容生产流程
        
        # 1. 模拟爬取评论
        comment = Comment(
            id="test_001",
            platform="douyin",
            content="昨天遇到了一件超级诡异的事情...",
            likes=100,
            timestamp=datetime.now(),
            keywords=["灵异"],
            status=ContentStatus.PENDING
        )
        
        # 2. 故事增强
        enhanced = await processor.enhance_story(comment.content, "悬疑")
        assert enhanced.enhanced_text
        
        # 3. 分镜生成
        scenes = await processor.generate_scenes(enhanced)
        assert len(scenes) > 0
        
        # 4. 视觉生成
        for scene in scenes:
            image = await visuals.generate_keyframe(scene.image_prompt, "悬疑")
            assert image.file_path
        
        # 5. 视频合成
        final_video = await synthesizer.compose_final_video(scenes, narration, bgm)
        assert final_video.video_path
        
        # 6. 多平台发布
        results = await distributor.batch_publish(final_video)
        assert all(result.status == "success" for result in results.values())
```

### 性能测试

```python
import asyncio
import time

class TestPerformance:
    async def test_concurrent_processing(self):
        # 测试并发处理能力
        start_time = time.time()
        
        tasks = []
        for i in range(10):
            task = processor.enhance_story(f"测试评论{i}", "都市")
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        assert len(results) == 10
        assert processing_time < 60  # 10个任务应在60秒内完成
    
    async def test_api_rate_limiting(self):
        # 测试API速率限制
        calls = []
        for i in range(100):
            call = visuals.generate_keyframe(f"测试提示词{i}", "现代")
            calls.append(call)
        
        # 应该能够处理速率限制而不失败
        results = await asyncio.gather(*calls, return_exceptions=True)
        
        # 检查是否有过多的失败
        failures = [r for r in results if isinstance(r, Exception)]
        assert len(failures) < len(results) * 0.1  # 失败率应低于10%
```

这个设计文档基于现有的开源项目MediaCrawler和social-auto-upload，提供了完整的技术架构、数据模型和实现策略。系统采用模块化设计，便于维护和扩展，同时包含了完善的错误处理和测试策略。