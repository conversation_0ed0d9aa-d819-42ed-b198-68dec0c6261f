"""
智能处理模块
集成DeepSeek LLM进行故事增强和分镜生成
"""

import uuid
import asyncio
from typing import List, Dict, Optional
from sqlalchemy.orm import Session

from app.core.config import DeepSeekConfig, settings
from app.core.database import get_db
from app.core.logger import logger
from app.models import Comment, EnhancedStory, Scene, ContentStatus


class ProcessorModule:
    """智能处理模块"""
    
    def __init__(self, config: DeepSeekConfig):
        self.config = config
        self.style_templates = self._load_style_templates()
        
    def _load_style_templates(self) -> Dict[str, Dict[str, str]]:
        """加载风格模板"""
        return {
            "悬疑惊悚": {
                "story_template": """
请将以下网友评论改写成一个引人入胜的悬疑惊悚故事，保持真实性的同时增强叙事性：

原始评论: {raw_comment}
字数要求: 200-500字

要求：
1. 保持故事的真实感，不要过度夸张
2. 增加悬疑氛围和紧张感
3. 丰富环境描写和心理描述
4. 确保故事结构完整，有开头、发展、高潮、结尾
5. 语言要生动有趣，适合短视频叙述
""",
                "scene_template": """
请将以下悬疑故事分解为3-5个视觉场景，每个场景包含图片提示词和视频提示词：

故事内容: {enhanced_story}

输出格式（请严格按照以下JSON格式）：
[
  {{
    "sequence": 1,
    "image_prompt": "描述静态画面构图、风格、人物、光影，悬疑氛围",
    "video_prompt": "描述动态动作、运镜、情绪变化，营造紧张感",
    "duration": 4.0
  }}
]
"""
            },
            "都市言情": {
                "story_template": """
请将以下网友评论改写成一个温馨浪漫的都市言情故事：

原始评论: {raw_comment}
字数要求: 200-500字

要求：
1. 突出情感元素和人物关系
2. 增加都市生活场景描写
3. 语言温馨自然，贴近生活
4. 适合短视频情感类内容
""",
                "scene_template": """
请将以下都市言情故事分解为3-5个视觉场景：

故事内容: {enhanced_story}

输出格式（请严格按照以下JSON格式）：
[
  {{
    "sequence": 1,
    "image_prompt": "描述都市场景、人物情感、温馨氛围",
    "video_prompt": "描述情感表达、人物互动、温馨画面",
    "duration": 4.0
  }}
]
"""
            },
            "搞笑日常": {
                "story_template": """
请将以下网友评论改写成一个幽默搞笑的日常故事：

原始评论: {raw_comment}
字数要求: 200-500字

要求：
1. 突出幽默元素和搞笑情节
2. 保持轻松愉快的语调
3. 增加生动的细节描写
4. 适合轻松娱乐类短视频
""",
                "scene_template": """
请将以下搞笑故事分解为3-5个视觉场景：

故事内容: {enhanced_story}

输出格式（请严格按照以下JSON格式）：
[
  {{
    "sequence": 1,
    "image_prompt": "描述搞笑场景、人物表情、幽默元素",
    "video_prompt": "描述搞笑动作、夸张表情、幽默效果",
    "duration": 4.0
  }}
]
"""
            }
        }
    
    async def enhance_story(self, raw_comment: str, style: str = "悬疑惊悚") -> EnhancedStory:
        """故事增强和润色"""
        try:
            logger.info(f"开始增强故事，风格: {style}")
            
            # TODO: 集成DeepSeek LLM实际调用
            # 这里先使用模拟数据进行开发
            enhanced_text = await self._mock_enhance_story(raw_comment, style)
            
            # 创建增强故事记录
            story = EnhancedStory(
                id=str(uuid.uuid4()),
                original_comment_id="",  # 需要从调用方传入
                enhanced_text=enhanced_text,
                style=style,
                word_count=len(enhanced_text)
            )
            
            logger.info(f"故事增强完成，字数: {story.word_count}")
            return story
            
        except Exception as e:
            logger.error(f"故事增强失败: {e}")
            raise
    
    async def _mock_enhance_story(self, raw_comment: str, style: str) -> str:
        """模拟故事增强（开发阶段使用）"""
        # 模拟LLM处理时间
        await asyncio.sleep(2)
        
        if "诡异" in raw_comment or "灵异" in raw_comment:
            return """
那是一个月黑风高的夜晚，我独自走在回家的小径上。街灯昏暗，投下长长的影子，仿佛有什么东西在暗中窥视。

突然，我听到身后传来脚步声，节奏和我的步伐完全一致。我停下来，脚步声也停了。我加快速度，它也加快。心跳开始加速，冷汗直冒。

我鼓起勇气回头一看，却什么也没有。但就在转身的瞬间，我感觉到一阵冰冷的气息从脖子后面掠过...

直到现在，我还是不知道那晚到底遇到了什么。但有一点我很确定，那绝对不是我的错觉。
"""
        elif "前任" in raw_comment:
            return """
分手三个月后，我以为已经彻底忘记了他。直到那天，他突然出现在我公司楼下。

"我想要回我送你的东西。"他说得很认真，仿佛那些礼物是什么珍贵的古董。

"连这支笔也要？"我拿出那支普通的圆珠笔，简直不敢相信。

"对，尤其是这支笔。"他的表情严肃得像在处理什么重要的商业谈判。

那一刻我突然明白，为什么我们会分手。有些人的奇葩程度，真的超出了正常人的想象范围。
"""
        else:
            return """
今天发生的事情，让我重新定义了什么叫做"社死现场"。

电梯里只有我和公司的大老板，气氛本来就很尴尬。偏偏这时候，我的肚子开始不争气地咕咕叫，声音大得像打雷一样。

更要命的是，老板居然很认真地问我："小王，你是不是没吃早餐？"

我红着脸正想解释，电梯突然停了，我们被困在了里面。接下来的二十分钟，我的肚子就像开了演唱会一样，各种声音轮番上演。

现在想起来，我觉得我可能需要换个工作了...
"""
    
    async def generate_scenes(self, story: EnhancedStory) -> List[Scene]:
        """智能分镜生成"""
        try:
            logger.info(f"开始生成分镜，故事ID: {story.id}")
            
            # TODO: 集成DeepSeek LLM实际调用
            # 这里先使用模拟数据进行开发
            scene_data = await self._mock_generate_scenes(story.enhanced_text, story.style)
            
            scenes = []
            for i, scene_info in enumerate(scene_data):
                scene = Scene(
                    id=str(uuid.uuid4()),
                    story_id=story.id,
                    sequence=i + 1,
                    image_prompt=scene_info["image_prompt"],
                    video_prompt=scene_info["video_prompt"],
                    duration=scene_info.get("duration", 4.0)
                )
                scenes.append(scene)
            
            logger.info(f"分镜生成完成，共 {len(scenes)} 个场景")
            return scenes
            
        except Exception as e:
            logger.error(f"分镜生成失败: {e}")
            raise
    
    async def _mock_generate_scenes(self, story_text: str, style: str) -> List[Dict]:
        """模拟分镜生成（开发阶段使用）"""
        # 模拟LLM处理时间
        await asyncio.sleep(3)
        
        if "诡异" in story_text or "灵异" in story_text:
            return [
                {
                    "image_prompt": "夜晚昏暗的街道，路灯投下长长的影子，悬疑氛围浓厚，电影级画质",
                    "video_prompt": "镜头缓慢推进，展现空旷的街道，营造紧张悬疑的氛围",
                    "duration": 4.0
                },
                {
                    "image_prompt": "一个人独自行走的背影，周围环境阴森，光影对比强烈",
                    "video_prompt": "跟拍人物行走，突然停顿，回头张望的动作，表现内心恐惧",
                    "duration": 4.0
                },
                {
                    "image_prompt": "空无一人的街道特写，只有风吹过的声音，恐怖氛围",
                    "video_prompt": "镜头快速转向，展现空旷场景，营造突然的惊悚效果",
                    "duration": 3.0
                }
            ]
        else:
            return [
                {
                    "image_prompt": "现代都市办公楼外景，阳光明媚，生活气息浓厚",
                    "video_prompt": "镜头从远景推近，展现都市生活场景",
                    "duration": 4.0
                },
                {
                    "image_prompt": "两个人面对面交谈的场景，表情自然，生活化场景",
                    "video_prompt": "对话场景，展现人物表情和情感交流",
                    "duration": 4.0
                },
                {
                    "image_prompt": "主人公恍然大悟的表情特写，情感丰富",
                    "video_prompt": "特写镜头，展现人物内心变化和情感转折",
                    "duration": 3.0
                }
            ]
    
    async def process_comment(self, comment_id: str, style: str = "悬疑惊悚") -> Dict:
        """处理单个评论的完整流程"""
        try:
            db = next(get_db())
            
            # 获取评论
            comment = db.query(Comment).filter(Comment.id == comment_id).first()
            if not comment:
                raise ValueError(f"评论不存在: {comment_id}")
            
            # 更新状态为处理中
            comment.status = ContentStatus.PROCESSING.value
            db.commit()
            
            try:
                # 故事增强
                enhanced_story = await self.enhance_story(comment.content, style)
                enhanced_story.original_comment_id = comment_id
                
                db.add(enhanced_story)
                db.commit()
                
                # 生成分镜
                scenes = await self.generate_scenes(enhanced_story)
                
                for scene in scenes:
                    db.add(scene)
                
                db.commit()
                
                # 更新评论状态
                comment.status = ContentStatus.ENHANCED.value
                db.commit()
                
                logger.info(f"评论处理完成: {comment_id}")
                
                return {
                    "comment_id": comment_id,
                    "story_id": enhanced_story.id,
                    "scenes_count": len(scenes),
                    "status": "success"
                }
                
            except Exception as e:
                # 处理失败，更新状态
                comment.status = ContentStatus.FAILED.value
                db.commit()
                raise
                
        except Exception as e:
            logger.error(f"处理评论失败: {e}")
            raise
        finally:
            db.close()


# 全局处理器实例
processor_module = ProcessorModule(DeepSeekConfig())