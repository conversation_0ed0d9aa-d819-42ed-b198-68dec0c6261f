"""
基础数据模型
"""

from datetime import datetime
from enum import Enum
from sqlalchemy import Column, String, Integer, DateTime, Text, Float, Boolean
from sqlalchemy.sql import func

from app.core.database import Base


class ContentStatus(Enum):
    """内容状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    ENHANCED = "enhanced"
    VISUAL_GENERATED = "visual_generated"
    VIDEO_SYNTHESIZED = "video_synthesized"
    PUBLISHED = "published"
    FAILED = "failed"


class AssetType(Enum):
    """资产类型枚举"""
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"


class Platform(Enum):
    """平台枚举"""
    DOUYIN = "douyin"
    BILIBILI = "bilibili"
    XIAOHONGSHU = "xiaohongshu"


class BaseModel(Base):
    """基础模型类"""
    __abstract__ = True
    
    id = Column(String, primary_key=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    def to_dict(self):
        """转换为字典"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            elif isinstance(value, Enum):
                value = value.value
            result[column.name] = value
        return result