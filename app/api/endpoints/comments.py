"""
评论相关API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models import Comment, ContentStatus
from app.core.logger import logger

router = APIRouter()


@router.get("/", response_model=List[dict])
async def get_comments(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    platform: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取评论列表"""
    try:
        query = db.query(Comment)
        
        if platform:
            query = query.filter(Comment.platform == platform)
        
        if status:
            query = query.filter(Comment.status == status)
        
        comments = query.offset(skip).limit(limit).all()
        return [comment.to_dict() for comment in comments]
    
    except Exception as e:
        logger.error(f"获取评论列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取评论列表失败")


@router.get("/{comment_id}")
async def get_comment(comment_id: str, db: Session = Depends(get_db)):
    """获取单个评论详情"""
    try:
        comment = db.query(Comment).filter(Comment.id == comment_id).first()
        if not comment:
            raise HTTPException(status_code=404, detail="评论不存在")
        
        return comment.to_dict()
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取评论详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取评论详情失败")


@router.put("/{comment_id}/status")
async def update_comment_status(
    comment_id: str, 
    status: str,
    db: Session = Depends(get_db)
):
    """更新评论状态"""
    try:
        # 验证状态值
        valid_statuses = [s.value for s in ContentStatus]
        if status not in valid_statuses:
            raise HTTPException(status_code=400, detail=f"无效的状态值，有效值: {valid_statuses}")
        
        comment = db.query(Comment).filter(Comment.id == comment_id).first()
        if not comment:
            raise HTTPException(status_code=404, detail="评论不存在")
        
        comment.status = status
        db.commit()
        
        logger.info(f"评论状态更新成功: {comment_id} -> {status}")
        return {"message": "状态更新成功", "comment_id": comment_id, "status": status}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新评论状态失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="更新评论状态失败")


@router.delete("/{comment_id}")
async def delete_comment(comment_id: str, db: Session = Depends(get_db)):
    """删除评论"""
    try:
        comment = db.query(Comment).filter(Comment.id == comment_id).first()
        if not comment:
            raise HTTPException(status_code=404, detail="评论不存在")
        
        db.delete(comment)
        db.commit()
        
        logger.info(f"评论删除成功: {comment_id}")
        return {"message": "评论删除成功", "comment_id": comment_id}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除评论失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="删除评论失败")


@router.get("/stats/summary")
async def get_comments_stats(db: Session = Depends(get_db)):
    """获取评论统计信息"""
    try:
        total = db.query(Comment).count()
        
        stats_by_platform = {}
        stats_by_status = {}
        
        # 按平台统计
        platforms = db.query(Comment.platform).distinct().all()
        for (platform,) in platforms:
            count = db.query(Comment).filter(Comment.platform == platform).count()
            stats_by_platform[platform] = count
        
        # 按状态统计
        statuses = db.query(Comment.status).distinct().all()
        for (status,) in statuses:
            count = db.query(Comment).filter(Comment.status == status).count()
            stats_by_status[status] = count
        
        return {
            "total": total,
            "by_platform": stats_by_platform,
            "by_status": stats_by_status
        }
    
    except Exception as e:
        logger.error(f"获取评论统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取评论统计失败")