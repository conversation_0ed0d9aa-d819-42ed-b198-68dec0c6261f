"""
场景数据模型
"""

from sqlalchemy import Column, String, Text, Integer, Float, ForeignKey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Scene(BaseModel):
    """场景模型"""
    __tablename__ = "scenes"
    
    story_id = Column(String, ForeignKey("enhanced_stories.id"), nullable=False, comment="故事ID")
    sequence = Column(Integer, nullable=False, comment="场景序号")
    image_prompt = Column(Text, nullable=False, comment="图片提示词")
    video_prompt = Column(Text, nullable=False, comment="视频提示词")
    duration = Column(Float, comment="建议时长（秒）")
    
    # 关联关系
    story = relationship("EnhancedStory", back_populates="scenes")
    assets = relationship("GeneratedAsset", back_populates="scene", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Scene(id={self.id}, story_id={self.story_id}, sequence={self.sequence})>"