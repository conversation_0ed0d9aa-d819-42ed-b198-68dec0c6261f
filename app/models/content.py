"""
最终内容数据模型
"""

from sqlalchemy import Column, String, Text, Integer, Float, ForeignKey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class FinalContent(BaseModel):
    """最终内容模型"""
    __tablename__ = "final_contents"
    
    story_id = Column(String, ForeignKey("enhanced_stories.id"), nullable=False, comment="故事ID")
    video_path = Column(String, nullable=False, comment="视频文件路径")
    thumbnail_path = Column(String, comment="缩略图路径")
    duration = Column(Float, comment="视频时长（秒）")
    file_size = Column(Integer, comment="文件大小（字节）")
    
    # 关联关系
    story = relationship("EnhancedStory", back_populates="final_contents")
    publish_records = relationship("PublishRecord", back_populates="content", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<FinalContent(id={self.id}, story_id={self.story_id}, duration={self.duration})>"