"""
故事相关API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models import EnhancedStory, Comment
from app.core.logger import logger

router = APIRouter()


@router.get("/", response_model=List[dict])
async def get_stories(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    style: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取故事列表"""
    try:
        query = db.query(EnhancedStory)
        
        if style:
            query = query.filter(EnhancedStory.style == style)
        
        stories = query.offset(skip).limit(limit).all()
        return [story.to_dict() for story in stories]
    
    except Exception as e:
        logger.error(f"获取故事列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取故事列表失败")


@router.get("/{story_id}")
async def get_story(story_id: str, db: Session = Depends(get_db)):
    """获取单个故事详情"""
    try:
        story = db.query(EnhancedStory).filter(EnhancedStory.id == story_id).first()
        if not story:
            raise HTTPException(status_code=404, detail="故事不存在")
        
        # 获取关联的原始评论
        original_comment = db.query(Comment).filter(Comment.id == story.original_comment_id).first()
        
        story_dict = story.to_dict()
        if original_comment:
            story_dict["original_comment"] = original_comment.to_dict()
        
        return story_dict
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取故事详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取故事详情失败")


@router.get("/{story_id}/scenes")
async def get_story_scenes(story_id: str, db: Session = Depends(get_db)):
    """获取故事的场景列表"""
    try:
        from app.models import Scene
        
        story = db.query(EnhancedStory).filter(EnhancedStory.id == story_id).first()
        if not story:
            raise HTTPException(status_code=404, detail="故事不存在")
        
        scenes = db.query(Scene).filter(Scene.story_id == story_id).order_by(Scene.sequence).all()
        return [scene.to_dict() for scene in scenes]
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取故事场景失败: {e}")
        raise HTTPException(status_code=500, detail="获取故事场景失败")


@router.delete("/{story_id}")
async def delete_story(story_id: str, db: Session = Depends(get_db)):
    """删除故事"""
    try:
        story = db.query(EnhancedStory).filter(EnhancedStory.id == story_id).first()
        if not story:
            raise HTTPException(status_code=404, detail="故事不存在")
        
        db.delete(story)
        db.commit()
        
        logger.info(f"故事删除成功: {story_id}")
        return {"message": "故事删除成功", "story_id": story_id}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除故事失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="删除故事失败")


@router.get("/stats/summary")
async def get_stories_stats(db: Session = Depends(get_db)):
    """获取故事统计信息"""
    try:
        total = db.query(EnhancedStory).count()
        
        stats_by_style = {}
        
        # 按风格统计
        styles = db.query(EnhancedStory.style).distinct().all()
        for (style,) in styles:
            count = db.query(EnhancedStory).filter(EnhancedStory.style == style).count()
            stats_by_style[style] = count
        
        # 平均字数
        avg_word_count = db.query(EnhancedStory.word_count).filter(EnhancedStory.word_count.isnot(None)).all()
        avg_words = sum(wc[0] for wc in avg_word_count) / len(avg_word_count) if avg_word_count else 0
        
        return {
            "total": total,
            "by_style": stats_by_style,
            "average_word_count": round(avg_words, 2)
        }
    
    except Exception as e:
        logger.error(f"获取故事统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取故事统计失败")